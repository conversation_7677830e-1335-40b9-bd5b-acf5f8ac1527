/**
 * @file application.h
 * @brief ESP32S3 AiBox应用程序核心类声明
 *
 * 本文件定义了AiBox设备的核心应用类Application，该类采用单例模式设计，
 * 负责整个设备的状态管理、音频处理、网络通信、IoT设备控制等核心功能。
 *
 * 主要功能模块：
 * - 设备状态机管理（空闲、监听、播放、升级等状态）
 * - 音频处理流水线（采集、处理、编码、解码、播放）
 * - 网络通信协议（MQTT、WebSocket）
 * - 语音唤醒和语音活动检测
 * - IoT设备集成和控制
 * - OTA固件升级管理
 * - 多线程任务调度和事件处理
 *
 * @note 该类是整个应用程序的核心，所有主要功能都通过此类协调
 * @warning 由于采用单例模式，需要注意线程安全和资源管理
 *
 * <AUTHOR> AiBox Team
 * @date 2024
 */

#ifndef _APPLICATION_H_
#define _APPLICATION_H_

// ============================================================================
// 系统和FreeRTOS相关头文件
// ============================================================================
#include <freertos/FreeRTOS.h>
#include <freertos/event_groups.h>
#include <freertos/task.h>

// ============================================================================
// C++标准库头文件
// ============================================================================
#include <string>
#include <mutex>
#include <list>
#include <deque>
#include <array>
#include <atomic>
#include <chrono>

// ============================================================================
// 音频处理相关头文件
// ============================================================================
#include <audio_decode_queue.h>
#include <opus_encoder.h>
#include <opus_decoder.h>
#include <opus_resampler.h>

// ============================================================================
// 应用层模块头文件
// ============================================================================
#include "protocol.h"
#include "ota.h"
#include "background_task.h"

// ============================================================================
// ESP32系统相关头文件
// ============================================================================
#include "esp_rom_sys.h"  ///< 微秒级别延时功能
#include "esp_wifi.h"

// ============================================================================
// 项目配置和平台特定头文件
// ============================================================================
#include "project_config.h"  ///< 全局宏定义管理

#if CONFIG_IDF_TARGET_ESP32S3
#include "wake_word_detect.h"
#include "audio_processor.h"
#endif

// ============================================================================
// 事件组位定义
// 用于FreeRTOS事件组的事件位标识，实现线程间同步
// ============================================================================

/// 主任务调度事件位，用于触发Schedule()调度的任务执行
#define SCHEDULE_EVENT (1 << 0)

/// 音频输入就绪事件位，由音频编解码器中断触发
#define AUDIO_INPUT_READY_EVENT (1 << 1)

/// 音频输出就绪事件位，用于音频播放同步（当前未使用）
#define AUDIO_OUTPUT_READY_EVENT (1 << 2)

/**
 * @brief 设备状态枚举
 *
 * 定义了AiBox设备的所有可能状态，用于状态机管理。
 * 状态转换遵循特定的规则，确保设备行为的一致性和可预测性。
 *
 * 状态转换：
 * Unknown → Starting → WiFiConfiguring/Idle → Connecting → Listening ⇄ Speaking
 *                                          ↓
 *                                      Upgrading
 *                                          ↓
 *                                    (重启到Starting)
 *
 * 特殊状态：
 * - Offline: 网络断开时的本地模式
 * - Moaning: 本地音频播放模式
 * - FatalError: 不可恢复的错误状态
 */
enum DeviceState {
    kDeviceStateUnknown,        ///< 未知状态，设备初始化时的默认状态
    kDeviceStateStarting,       ///< 启动中，设备正在进行硬件和软件初始化
    kDeviceStateWifiConfiguring,///< WiFi配置中，设备等待用户配置网络连接
    kDeviceStateIdle,           ///< 空闲状态，设备已就绪但未激活语音功能
    kDeviceStateConnecting,     ///< 连接中，设备正在建立与服务器的连接
    kDeviceStateListening,      ///< 监听中，设备正在等待和处理用户语音输入
    kDeviceStateSpeaking,       ///< 播放中，设备正在播放TTS音频或响应内容
    kDeviceStateUpgrading,      ///< 升级中，设备正在进行OTA固件升级
    kDeviceStateMoaning,        ///< 本地音频播放中，播放内置的音频文件
    kDeviceStateOffline,        ///< 离线状态，网络断开时的本地工作模式
    kDeviceStateFatalError      ///< 致命错误状态，设备遇到不可恢复的错误
};

// ============================================================================
// 音频处理相关常量定义
// ============================================================================

/// OPUS音频帧持续时间（毫秒）
/// 60ms帧长度在音质和延迟之间提供了良好的平衡
/// 较长的帧长度可以提高压缩效率，但会增加延迟
#define OPUS_FRAME_DURATION_MS 60

/**
 * @brief AiBox应用程序核心类
 *
 * Application类是整个AiBox设备的核心控制器，采用单例模式设计。
 * 该类负责协调所有主要功能模块，包括音频处理、网络通信、状态管理、
 * IoT设备控制等。所有的业务逻辑都通过这个类进行统一管理和调度。
 *
 * 核心特性：
 * - 单例模式：确保全局唯一的应用实例
 * - 事件驱动：基于FreeRTOS事件组的异步事件处理
 * - 多线程安全：通过互斥锁和事件同步保证线程安全
 * - 状态机管理：完整的设备状态转换和管理机制
 * - 模块化设计：清晰的功能模块划分和接口抽象
 *
 * 线程模型：
 * - 主循环线程：处理事件调度和状态管理
 * - 音频输入线程：高优先级实时音频处理
 * - 后台任务线程：处理OPUS编解码等耗时操作
 * - 监控线程：系统监控和自愈机制
 *
 * @note 该类的所有公有方法都是线程安全的
 * @warning 由于是单例模式，需要注意资源的正确释放
 */
class Application {
public:
    // ========================================================================
    // 单例模式接口
    // ========================================================================

    /**
     * @brief 获取Application类的唯一实例
     *
     * 使用C++11的线程安全静态局部变量实现单例模式。
     * 该方法保证在多线程环境下的安全性和唯一性。
     *
     * @return Application& 应用程序实例的引用
     * @thread_safety 线程安全，C++11保证静态局部变量的线程安全初始化
     */
    static Application& GetInstance() {
        static Application instance;
        return instance;
    }

    /// 删除拷贝构造函数，防止单例被复制
    Application(const Application&) = delete;

    /// 删除赋值运算符，防止单例被赋值
    Application& operator=(const Application&) = delete;

    // ========================================================================
    // 核心控制接口
    // ========================================================================

    /**
     * @brief 启动应用程序
     *
     * 初始化所有硬件和软件模块，创建工作线程，建立网络连接。
     * 这是应用程序的主要入口点，在main.cc中被调用。
     *
     * @note 该方法应该只被调用一次，在系统启动时
     * @thread_safety 应该在主线程中调用
     */
    void Start();

    /**
     * @brief 获取当前设备状态
     * @return DeviceState 当前的设备状态
     * @thread_safety 线程安全，原子读取
     */
    DeviceState GetDeviceState() const { return device_state_; }

    /**
     * @brief 获取设备低电量状态
     * @return bool true表示电量低，false表示电量正常
     * @thread_safety 线程安全，原子读取
     */
    bool GetDeviceLowBState() const { return batter_state_; }

    /**
     * @brief 检查是否检测到语音活动
     * @return bool true表示检测到语音，false表示静音
     * @thread_safety 线程安全，原子读取
     */
    bool IsVoiceDetected() const { return voice_detected_; }

    /**
     * @brief 调度任务到主线程执行
     *
     * 将回调函数调度到主事件循环中执行，确保状态操作的线程安全。
     * 所有需要修改设备状态或访问共享资源的操作都应该通过此方法调度。
     *
     * @param callback 要执行的回调函数
     * @thread_safety 线程安全，可在任意线程中调用
     * @note 回调函数将在主线程中执行
     */
    void Schedule(std::function<void()> callback);

    /**
     * @brief 设置设备状态
     *
     * 安全地切换设备状态，执行相应的初始化和清理操作。
     * 包含状态转换保护机制，防止非法状态转换。
     *
     * @param state 目标设备状态
     * @thread_safety 应该通过Schedule()在主线程中调用
     * @see DeviceState 设备状态枚举
     */
    void SetDeviceState(DeviceState state);

    /**
     * @brief 显示警告信息并播放提示音
     *
     * 根据警告类型播放相应的提示音，支持多语言提示。
     * 主要用于网络错误、配置失败等场景的用户提示。
     *
     * @param title 警告标题
     * @param message 警告消息内容
     * @thread_safety 线程安全，可在任意线程中调用
     */
    void Alert(const std::string& title, const std::string& message);

    /**
     * @brief 中止当前的语音播放
     *
     * 立即停止当前正在播放的音频，清空播放队列。
     * 用于语音唤醒打断、用户取消等场景。
     *
     * @param reason 中止原因，用于日志记录和调试
     * @thread_safety 线程安全，可在任意线程中调用
     */
    void AbortSpeaking(AbortReason reason);

    // ========================================================================
    // 语音交互控制接口
    // ========================================================================

    /**
     * @brief 切换对话状态
     *
     * 在空闲和监听状态之间切换，用于手动激活语音功能。
     *
     * @thread_safety 线程安全，内部使用Schedule()调度
     */
    void ToggleChatState();

    /**
     * @brief 开始语音监听
     *
     * 激活语音监听功能，开始接收和处理用户语音输入。
     *
     * @thread_safety 线程安全，内部使用Schedule()调度
     */
    void StartListening();

    /**
     * @brief 停止语音监听
     *
     * 停止语音监听，切换到空闲状态。
     *
     * @thread_safety 线程安全，内部使用Schedule()调度
     */
    void StopListening();

    // ========================================================================
    // IoT和网络控制接口
    // ========================================================================

    /**
     * @brief 更新IoT设备状态
     *
     * 收集所有IoT设备的当前状态并发送到服务器。
     *
     * @thread_safety 应该在主线程中调用
     */
    void UpdateIotStates();

    /**
     * @brief 关闭音频通道
     *
     * 关闭与服务器的音频通道连接。
     *
     * @thread_safety 线程安全，内部使用Schedule()调度
     */
    void CloseAudioChannel();

    // ========================================================================
    // 音频处理工具接口
    // ========================================================================

    /**
     * @brief 应用音频变速处理
     *
     * 对PCM音频数据应用变速效果，不改变音调。
     *
     * @param pcm 输入的PCM音频数据
     * @param speed_factor 变速因子（1.0为正常速度，>1.0为加速，<1.0为减速）
     * @return std::vector<int16_t> 处理后的PCM音频数据
     * @thread_safety 线程安全，纯函数无副作用
     */
    std::vector<int16_t> ApplySpeedUp(const std::vector<int16_t>& pcm, float speed_factor);

    // ========================================================================
    // 系统监控和状态标志
    // ========================================================================

    /// 本地音频播放标志，用于IMU触发的音频播放
    bool moaning_flag_ = false;

    /**
     * @brief 监控音频停止条件和队列状态
     *
     * 运行在独立线程中，监控音频播放结束条件。
     *
     * @note 该方法包含无限循环，应在独立线程中运行
     * @thread_safety 线程安全，使用Schedule()与主线程通信
     */
    void MonitorStopAndAudioQueue();

    /// MQTT停止标志，原子操作保证线程安全
    std::atomic<bool> mqtt_stop_flag_{false};

    // ========================================================================
    // 条件编译的功能接口
    // ========================================================================

    #if WIFI_SIGNAL_CHECK_TONE == 1
    /**
     * @brief 获取协议实例指针（条件编译）
     * @return Protocol* 协议实例指针
     * @note 仅在启用WiFi信号检查功能时可用
     */
    Protocol* GetProtocol() { return protocol_.get(); }

    /**
     * @brief 获取音频解码队列引用（条件编译）
     * @return std::unique_ptr<AudioDecodeQueue>& 音频解码队列的引用
     * @note 仅在启用WiFi信号检查功能时可用
     */
    std::unique_ptr<AudioDecodeQueue>& GetAudioDecodeQueue() { return audio_decode_queue_; }
    #endif

    #if WIFI_CONNECT_CHECK_TONE == 1
    /**
     * @brief 播放网络断开提示音（条件编译）
     *
     * 当检测到网络连接断开时播放提示音。
     *
     * @note 仅在启用WiFi连接检查功能时可用
     * @thread_safety 线程安全，可在任意线程中调用
     */
    void PlayNetworkDisc();
    #endif

    


private:
    // ========================================================================
    // 构造函数和析构函数（私有，支持单例模式）
    // ========================================================================

    /**
     * @brief 私有构造函数
     *
     * 初始化应用程序的基本组件和默认参数。
     * 私有访问确保只能通过GetInstance()创建实例。
     */
    Application();

    /**
     * @brief 私有析构函数
     *
     * 清理应用程序占用的资源，包括线程、内存、硬件资源等。
     */
    ~Application();

    // ========================================================================
    // 系统配置常量
    // ========================================================================

    /// 最大静音时间（秒），超过此时间将切换到空闲状态
    static constexpr int max_silence_seconds = 10;

    /// 当前音量设置（0-100），默认80%
    int current_volume_ = 80;

    // ========================================================================
    // ESP32S3平台特有的音频处理模块（条件编译）
    // ========================================================================

    #if CONFIG_IDF_TARGET_ESP32S3
    /// 语音唤醒检测器，基于ESP-SR库实现本地唤醒词检测
    WakeWordDetect wake_word_detect_;

    /// 音频处理器，提供AEC（回声消除）、NS（噪声抑制）等功能
    AudioProcessor audio_processor_;
    #endif

    // ========================================================================
    // 核心功能模块实例
    // ========================================================================

    /// OTA升级管理器，负责固件版本检查和升级
    Ota ota_;

    /// 互斥锁，保护主任务队列的线程安全访问
    std::mutex mutex_;

    /// 主任务队列，存储通过Schedule()调度的待执行任务
    std::list<std::function<void()>> main_tasks_;

    /// 网络通信协议实例（MQTT或WebSocket），使用智能指针管理
    std::unique_ptr<Protocol> protocol_;

    /// FreeRTOS事件组句柄，用于线程间事件同步
    EventGroupHandle_t event_group_;

    // 备用的音频解码队列（已被AudioDecodeQueue替代）
    // std::deque<std::vector<uint8_t>> audio_decode_queue_;

    /// 音频缓冲区总大小，用于内存使用监控
    size_t total_buffered_size_{0};

    // ========================================================================
    // 设备状态和控制标志
    // ========================================================================

    /// 当前设备状态，使用volatile确保多线程可见性
    volatile DeviceState device_state_ = kDeviceStateUnknown;

    /// 电池低电量状态标志
    bool batter_state_ = false;

    /// 持续监听标志，控制是否保持语音监听状态
    bool keep_listening_ = false;

    /// 操作中止标志，用于中断当前操作
    bool aborted_ = false;

    /// 语音检测标志，标记是否检测到用户语音活动
    bool voice_detected_ = false;

    /// 上次IoT设备状态的JSON字符串，用于状态变化检测
    std::string last_iot_states_;

    /// 空闲超时时长（分钟），超过此时间无语音活动将切换到空闲状态
    int idle_timeout_duration_ = 10;
    // ========================================================================
    // 音频编解码和处理模块
    // ========================================================================

    /// 后台任务处理器，用于处理OPUS编解码等耗时操作
    /// 使用独立线程避免阻塞主线程和音频处理线程
    BackgroundTask* background_task_ = nullptr;

    /// 上次音频输出时间戳，用于音频同步和延迟计算
    std::chrono::steady_clock::time_point last_output_time_;

    /// OPUS音频编码器，将PCM音频数据编码为OPUS格式用于网络传输
    /// 配置：16kHz采样率，单声道，60ms帧长度
    std::unique_ptr<OpusEncoderWrapper> opus_encoder_;

    // 备用的OPUS解码器（已被AudioDecodeQueue内置解码器替代）
    // std::unique_ptr<OpusDecoderWrapper> opus_decoder_;

    /// OPUS解码器采样率，-1表示未初始化
    /// 根据服务器音频采样率动态设置
    int opus_decode_sample_rate_ = -1;

    /// 输入音频重采样器，将音频编解码器的采样率转换为16kHz
    OpusResampler input_resampler_;

    /// 参考信号重采样器，用于AEC（回声消除）的参考信号处理
    OpusResampler reference_resampler_;

    /// 输出音频重采样器，将解码后的音频转换为音频编解码器的输出采样率
    OpusResampler output_resampler_;

    /// 音频解码播放队列，管理接收到的音频数据的解码和播放
    /// 支持OPUS解码、PCM播放、队列管理等功能
    std::unique_ptr<AudioDecodeQueue> audio_decode_queue_;

    // ========================================================================
    // 音频缓冲和时序管理
    // ========================================================================

    /// 上次检测到语音活动的时间戳，用于静音超时计算
    std::chrono::steady_clock::time_point last_voice_time_;

    /// 音频缓冲区数量，使用多缓冲机制提高音频处理效率
    static const size_t BUFFER_COUNT = 3;

    /// 音频缓冲区数组，每个缓冲区存储一帧PCM音频数据
    std::array<std::vector<int16_t>, BUFFER_COUNT> audio_buffers_;

    /// 缓冲区就绪标志数组，原子操作保证线程安全
    std::atomic<bool> buffer_ready_[BUFFER_COUNT] = {false, false, false};

    /// 当前使用的缓冲区索引
    int current_buffer_ = 0;

    /// 播放后停止标志，用于控制音频播放完成后的状态转换
    std::atomic<bool> stop_after_playback_{false};

    /// 播放开始时间戳，用于播放时长统计和超时检测
    std::chrono::steady_clock::time_point playback_start_time_;
    void ImuRead();
    void MainLoop();
    void InputAudioLoop();
    void InputAudio();
    void OutputAudio();
    void ResetDecoder();
    void SetDecodeSampleRate(int sample_rate);
    void CheckNewVersion();

#if WIFI_SIGNAL_CHECK_TONE == 1
    void MonitorWifiRssi();
    void HandleWifiWeak();
#endif

    // void PlayLocalFile(const char* data, size_t size);
    // 保存语言类型到 NVS
    void SaveLanguageTypeToNVS(const std::string& language);
    void SaveVolumeToNVS(int volume);
    int LoadVolumeFromNVS(int default_value);
    std::string GetLanguageFromNVS();
    std::pair<bool,std::string> moaning_state_ {false,""};
    void PlayOpusFile(const char* data, size_t size);
    static constexpr int kVolumeStep = 10;
    static constexpr int kVolumeMax = 100;
    static constexpr int kVolumeMin = 60;
    int silence_count_  =0;
    bool state_sepeaking_ = false;
    bool oudio_output_finish_ =true;
    bool audio_channel_close_=false;
    bool wake_stage_end_=false;
    uint32_t wake_stage_end_cnt_=0;
    double batter_volo_ = 4.2f; 
    // bool cancel_tts_sent_ = false; // 标记当前listening周期是否已发送CancelTTS
    bool start_play_voice_ =  false;
    int start_play_voice_num_  = 0;
    int touch_value_= 0;
    bool last_batter_state_  =false;

    std::chrono::steady_clock::time_point last_audio_received_time_;

    bool OFFLINE_FLAG = false;
    bool NetOff_OFFLINE_FLAG = false; 

    
    

};

#endif // _APPLICATION_H_
