#include "button.h"
#include "driver/gpio.h"
#include <esp_log.h>

#include "board.h"
#include "esp_system.h"
static const char* TAG = "Button";

uint32_t kPoserOffsetCount =0 ;
Button::Button(gpio_num_t gpio_num,bool disable_pull1, bool active_high) : gpio_num_(gpio_num) {
    if (gpio_num == GPIO_NUM_NC) {
        return;
    }
    button_config_t button_config = {
        .type = BUTTON_TYPE_GPIO,
        .long_press_time = 3000,
        .short_press_time = 50,
        .gpio_button_config = {
            .gpio_num = gpio_num,
            .active_level = static_cast<uint8_t>(active_high ? 0 : 1),
            .disable_pull = disable_pull1  // 禁用内部上拉和下拉
        }
    };
    button_handle_ = iot_button_create(&button_config);




    {
      // 先放在里，这个口要控制电源关闭，假如电源块煤电。
      gpio_set_direction(GPIO_NUM_4, GPIO_MODE_OUTPUT);

      gpio_set_level(GPIO_NUM_4, 1);  // 1 表示高电平，0 表示低电平
                                      // 设置 GPIO23 为高电平
    }
    {
      //   gpio_config_t io_conf = {.pin_bit_mask = (1ULL << GPIO_NUM_48),
      //                            .mode = GPIO_MODE_INPUT,
      //                            .pull_up_en = GPIO_PULLUP_DISABLE,
      //                            .pull_down_en = GPIO_PULLDOWN_DISABLE,
      //                            .intr_type = GPIO_INTR_DISABLE};
      //   gpio_config(&io_conf);

      //   button_config_t button_config = {
      //       .type = BUTTON_TYPE_GPIO,
      //       .long_press_time = 3000,
      //       .short_press_time = 50,
      //       .gpio_button_config = {
      //           .gpio_num = GPIO_NUM_48,
      //           .active_level = static_cast<uint8_t>(active_high ? 0 : 1),
      //           .disable_pull = true  // 禁用内部上拉和下拉
      //       }};
      //  auto  button_handle_temp = iot_button_create(&button_config);
      gpio_set_direction(GPIO_NUM_48, GPIO_MODE_INPUT);
    }

    if (button_handle_ == NULL) {
      ESP_LOGE(TAG, "Failed to create button handle");
      return;
    }
}
//

/**
 * @brief 设置设备电源状态
 *
 * 控制设备的电源开关，用于实现设备的关机和省电功能。
 * 该函数在以下情况下被调用：
 * - 设备长时间待机需要关机节省电量
 * - 电池电量过低需要保护性关机
 * - 用户主动请求关机
 * - 系统异常需要强制关机
 *
 * 执行的操作：
 * 1. 关闭LED指示灯，减少电量消耗
 * 2. 控制GPIO_NUM_4引脚电平，切断主电源
 *
 * 硬件控制逻辑：
 * - GPIO_NUM_4控制主电源开关电路
 * - 高电平(1)：保持电源开启
 * - 低电平(0)：切断电源，设备关机
 *
 * @param a 电源控制标志
 *          - true(1): 保持电源开启
 *          - false(0): 切断电源关机
 *
 * @note 该函数调用后设备可能会立即关机，确保在调用前保存重要数据
 * @note LED关闭操作确保关机前停止所有指示灯，避免电量浪费
 * @hardware_requirement 需要GPIO_NUM_4连接到电源控制电路
 * @power_management 这是设备电源管理的核心函数
 *
 * @see Board::GetLed() 获取LED控制器
 * @see gpio_set_level() ESP-IDF GPIO电平控制API
 */
void SetPowerOffset(bool a) {
  // === 关闭LED指示灯 ===
  // 在关机前关闭所有LED，减少电量消耗并提供关机视觉反馈
  auto led = Board::GetInstance().GetLed();
  led->ShuntDown();  // 执行LED关闭操作

  // === 控制主电源开关 ===
  // 通过GPIO_NUM_4控制电源开关电路
  // 待机太长、语音关键、电量太低时需要关机保护
  gpio_set_level(GPIO_NUM_4, a);  // 1表示高电平(开机)，0表示低电平(关机)
}

//
Button::~Button() {
    if (button_handle_ != NULL) {
        iot_button_delete(button_handle_);
    }
}



void Button::OnPressDown(std::function<void()> callback) {
    if (button_handle_ == nullptr) {
        return;
    }
    on_press_down_ = callback;
    iot_button_register_cb(button_handle_, BUTTON_PRESS_DOWN, [](void* handle, void* usr_data) {
        Button* button = static_cast<Button*>(usr_data);
        if (button->on_press_down_) {
            ESP_LOGI(TAG, "Button Press Down Detected");

            button->on_press_down_();
           
        }
    }, this);
}

void Button::OnPressUp(std::function<void()> callback) {
    if (button_handle_ == nullptr) {
        return;
    }
    on_press_up_ = callback;
    iot_button_register_cb(button_handle_, BUTTON_PRESS_UP, [](void* handle, void* usr_data) {
        Button* button = static_cast<Button*>(usr_data);
        if (button->on_press_up_) {
            ESP_LOGI(TAG, "Button Press Up Detected");
            button->on_press_up_();
        }
    }, this);
}

void Button::OnLongPress(std::function<void()> callback) {
    if (button_handle_ == nullptr) {
        return;
    }
    on_long_press_ = callback;
    iot_button_register_cb(button_handle_, BUTTON_LONG_PRESS_HOLD, [](void* handle, void* usr_data) {
        Button* button = static_cast<Button*>(usr_data);
        if (button->on_long_press_) {
            ESP_LOGI(TAG, "Button Long Press Detected");
            button->on_long_press_();
        }
    }, this);
}

void Button::OnClick(std::function<void()> callback) {
    if (button_handle_ == nullptr) {
        return;
    }
    on_click_ = callback;
    iot_button_register_cb(button_handle_, BUTTON_SINGLE_CLICK, [](void* handle, void* usr_data) {
        Button* button = static_cast<Button*>(usr_data);
        if (button->on_click_) {
            ESP_LOGI(TAG, "Button Single Click Detected");
            button->on_click_();
        }
    }, this);
}

void Button::OnDoubleClick(std::function<void()> callback) {
    if (button_handle_ == nullptr) {
        return;
    }
    on_double_click_ = callback;
    iot_button_register_cb(button_handle_, BUTTON_DOUBLE_CLICK, [](void* handle, void* usr_data) {
        Button* button = static_cast<Button*>(usr_data);
        if (button->on_double_click_) {
            ESP_LOGI(TAG, "Button Double Click Detected");
            button->on_double_click_();
        }
    }, this);
}
